<template>
  <div class="app-container gap-4 flex flex-wrap flex-col">
    <!-- 声音控制区域 -->
    <div class="flex gap-4">
      <div class="w-1/2">
        <el-card class="filter-container">
          <div>
            <label for="volume-control"> 声音控制 </label>
            <div class="flex gap-4 items-center mt-2">
              <el-input-number
                v-model="volumeValue"
                :min="0"
                :max="15"
                placeholder="音量大小"
                style="width: 200px"
              />
              <el-button
                @click="handleVolumeChange"
                :loading="loadingVolume"
                type="primary"
              >
                设置音量
              </el-button>
            </div>
          </div>
          <div class="mt-4">
            <label>选择终端</label>
            <el-select
              v-model="selectedClients"
              multiple
              placeholder="请选择终端"
              style="width: 100%; margin-top: 8px"
              :loading="loadingClients"
            >
              <el-option
                v-for="client in clientList"
                :key="client.id"
                :label="client.name"
                :value="client.id"
              />
            </el-select>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 在线终端列表 -->
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span>在线终端管理</span>
          <el-button
            @click="refreshClients"
            :loading="loadingClients"
            icon="Refresh"
          >
            刷新
          </el-button>
        </div>
      </template>

      <el-table v-loading="loadingClients" :data="clientList" stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          label="终端名称"
          align="center"
          prop="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="IP地址"
          align="center"
          prop="ip"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="MAC地址"
          align="center"
          prop="mark"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="分组名称"
          align="center"
          prop="groupName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="连接时间"
          align="center"
          prop="connectTime"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            {{ formatConnectTime(scope.row.connectTime) }}
          </template>
        </el-table-column>
        <el-table-column
          label="下载状态"
          align="center"
          prop="downloadState"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <el-tag :type="scope.row.downloadState === 1 ? 'success' : 'info'">
              {{ scope.row.downloadState === 1 ? "已下载" : "未下载" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="在线状态"
          align="center"
          prop="status"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? "在线" : "离线" }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="clientTotal > 0"
        :total="clientTotal"
        v-model:page="clientQueryParams.pageNo"
        v-model:limit="clientQueryParams.pageSize"
        @pagination="handleClientPagination"
      />
    </el-card>

    <!-- 节目管理 -->
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span>节目管理</span>
          <el-button
            @click="refreshItems"
            :loading="loadingItems"
            icon="Refresh"
          >
            刷新
          </el-button>
        </div>
      </template>

      <el-table v-loading="loadingItems" :data="itemList" stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          label="节目名称"
          align="center"
          prop="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="文件名"
          align="center"
          prop="fileName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="节目时长"
          align="center"
          prop="stime"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            {{ formatDuration(scope.row.stime) }}
          </template>
        </el-table-column>
        <el-table-column
          label="节目大小"
          align="center"
          prop="itemLeng"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            {{ formatFileSize(scope.row.itemLeng) }}
          </template>
        </el-table-column>
        <el-table-column
          label="节目类型"
          align="center"
          prop="itemtype"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            {{ formatItemType(scope.row.itemtype) }}
          </template>
        </el-table-column>
        <el-table-column
          label="播放次数"
          align="center"
          prop="playCount"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建用户"
          align="center"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="bdate"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="状态"
          align="center"
          prop="state"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <el-tag :type="scope.row.state === 1 ? 'success' : 'info'">
              {{ scope.row.state === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="itemTotal > 0"
        :total="itemTotal"
        v-model:page="itemQueryParams.pageNo"
        v-model:limit="itemQueryParams.pageSize"
        @pagination="handleItemPagination"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, computed } from "vue";
import { changeVolume, getItem, getOnClient } from "@/api/info";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();

// 响应式数据
const volumeValue = ref(5);
const selectedClients = ref([]);
const loadingVolume = ref(false);
const loadingClients = ref(false);
const loadingItems = ref(false);

// 终端列表相关
const clientList = ref([]);
const clientTotal = ref(0);
const clientQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: 1, // 这里需要根据实际登录用户获取
});

// 节目列表相关
const itemList = ref([]);
const itemTotal = ref(0);
const itemQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: 1, // 这里需要根据实际登录用户获取
});

// 初始化
onMounted(() => {
  getClientList();
  getItemList();
});

// 获取在线终端列表
function getClientList() {
  loadingClients.value = true;
  getOnClient(clientQueryParams)
    .then((response) => {
      // 根据接口文档，直接返回对象数组
      if (response.data && Array.isArray(response.data)) {
        clientList.value = response.data;
        clientTotal.value = response.data.length;
      } else if (response.data) {
        // 如果返回单个对象，转换为数组
        clientList.value = [response.data];
        clientTotal.value = 1;
      } else {
        clientList.value = [];
        clientTotal.value = 0;
      }
      loadingClients.value = false;
    })
    .catch((error) => {
      loadingClients.value = false;
      console.error("获取终端列表失败:", error);
      ElMessage.error("获取终端列表失败");
    });
}

// 获取节目列表
function getItemList() {
  loadingItems.value = true;
  getItem(itemQueryParams)
    .then((response) => {
      // 根据接口文档，直接返回对象数组
      if (response.data && Array.isArray(response.data)) {
        itemList.value = response.data;
        itemTotal.value = response.data.length;
      } else if (response.data) {
        // 如果返回单个对象，转换为数组
        itemList.value = [response.data];
        itemTotal.value = 1;
      } else {
        itemList.value = [];
        itemTotal.value = 0;
      }
      loadingItems.value = false;
    })
    .catch((error) => {
      loadingItems.value = false;
      console.error("获取节目列表失败:", error);
      ElMessage.error("获取节目列表失败");
    });
}

// 声音控制
function handleVolumeChange() {
  if (selectedClients.value.length === 0) {
    ElMessage.warning("请先选择终端");
    return;
  }

  if (volumeValue.value < 0 || volumeValue.value > 15) {
    ElMessage.warning("音量值应在0-15之间");
    return;
  }

  loadingVolume.value = true;
  const params = {
    selectClient: selectedClients.value.join(" "),
    selectGroup: null,
    volume: volumeValue.value,
  };

  changeVolume(params)
    .then((response) => {
      // 根据接口文档，返回格式为 {code: 0, data: {}, msg: "string"}
      if (response.data && response.data.code === 0) {
        ElMessage.success("音量设置成功");
      } else {
        ElMessage.error(response.data?.msg || "音量设置失败");
      }
      loadingVolume.value = false;
    })
    .catch((error) => {
      loadingVolume.value = false;
      console.error("音量设置失败:", error);
      ElMessage.error("音量设置失败");
    });
}

// 刷新终端列表
function refreshClients() {
  clientQueryParams.pageNo = 1;
  getClientList();
}

// 刷新节目列表
function refreshItems() {
  itemQueryParams.pageNo = 1;
  getItemList();
}

// 处理终端列表分页
function handleClientPagination(pagination) {
  clientQueryParams.pageNo = pagination.page;
  clientQueryParams.pageSize = pagination.limit;
  getClientList();
}

// 处理节目列表分页
function handleItemPagination(pagination) {
  itemQueryParams.pageNo = pagination.page;
  itemQueryParams.pageSize = pagination.limit;
  getItemList();
}

// 格式化时长（秒转换为时分秒）
function formatDuration(seconds) {
  if (!seconds) return "0秒";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = "";
  if (hours > 0) result += `${hours}时`;
  if (minutes > 0) result += `${minutes}分`;
  if (secs > 0) result += `${secs}秒`;

  return result || "0秒";
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (!bytes) return "0 B";

  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
}

// 格式化节目类型
function formatItemType(type) {
  const typeMap = {
    0: "未知",
    1: "视频",
    2: "图片",
    3: "音频",
    4: "文档",
  };
  return typeMap[type] || "未知";
}

// 格式化连接时间
function formatConnectTime(timeData) {
  if (!timeData) return "--";

  try {
    // 如果是字符串，尝试解析为JSON
    if (typeof timeData === "string") {
      timeData = JSON.parse(timeData);
    }

    // 如果有time字段（时间戳），直接使用
    if (timeData.time) {
      const date = new Date(timeData.time);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    }

    // 如果没有time字段，从其他字段构造日期
    if (timeData.year !== undefined) {
      // year字段通常是从1900年开始计算的，需要加上1900
      const year = timeData.year + 1900;
      const month = timeData.month || 0; // month是从0开始的
      const date = timeData.date || 1;
      const hours = timeData.hours || 0;
      const minutes = timeData.minutes || 0;
      const seconds = timeData.seconds || 0;

      const fullDate = new Date(year, month, date, hours, minutes, seconds);
      return fullDate.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    }

    return "--";
  } catch (error) {
    console.error("格式化连接时间失败:", error);
    return "--";
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}

.filter-container label {
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  display: block;
}

.app-container {
  padding: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.el-card .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

.el-table {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 0;
}

.flex {
  display: flex;
}

.gap-4 {
  gap: 1rem;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.w-1\/2 {
  width: 50%;
}
</style>
