<template>
  <div class="app-container w-full h-full">
    <div v-if="loading" class="w-full h-full flex items-center justify-center">
      <el-loading-spinner />
      <span class="ml-2">正在加载视频流...</span>
    </div>
    <div v-else class="w-full h-full rounded" ref="cameraVideo"></div>
  </div>
</template>
<script setup>
import DPlayer from 'dplayer';
import Hls from "hls.js";
import { onMounted, ref } from "vue";
import { useRoute } from 'vue-router';
import { getCamera, getPreviewURLs } from '@/api/hardware/hkCamera';

const route = useRoute();
const cameraVideo = ref();
const loading = ref(true);

const initializePlayer = (container, url) => {
  new DPlayer({
    container: container,
    live: true,
    autoplay: true,
    theme: "#0093ff",
    loop: true,
    lang: "zh-cn",
    screenshot: true,
    hotkey: true,
    preload: "auto",
    volume: 0.7,
    video: {
      url: url,
      type: "customHls",
      customType: {
        customHls: (video, player) => {
          const hls = new Hls();
          hls.loadSource(video.src);
          hls.attachMedia(video);
        },
      },
    },
    mutex: false,
    pluginOptions: {
      hls: {
        // hls config
      },
    },
  });
};

onMounted(async () => {
  try {
    const cameraId = route.params.id;
    if (!cameraId) {
      console.error('摄像头ID不存在');
      return;
    }

    // 获取摄像头详情信息
    const cameraResponse = await getCamera(cameraId);
    if (cameraResponse.data) {
      const cameraIndexCode = cameraResponse.data.cameraIndexOde;

      if (cameraIndexCode) {
        // 获取预览视频流URL
        const urlResponse = await getPreviewURLs(cameraIndexCode);
        if (urlResponse && urlResponse.url) {
          initializePlayer(cameraVideo.value, urlResponse.url);
        } else {
          console.error('获取视频流URL失败');
          // 使用默认测试流作为备用
          initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
        }
      } else {
        console.error('摄像头编号不存在');
        // 使用默认测试流作为备用
        initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
      }
    }
  } catch (error) {
    console.error('加载摄像头视频失败:', error);
    // 使用默认测试流作为备用
    initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
  } finally {
    loading.value = false;
  }
});
</script>
