# 门禁告警菜单配置说明

## 功能概述
已创建门禁告警功能，需要在后端系统菜单中添加相应的菜单项。

## 前端文件已创建
- **页面文件**: `src/views/guard/warning/index.vue`
- **API接口**: `src/api/guard/index.js` (已添加 `listGuardWarning` 函数)

## 后端菜单配置
需要在后端系统菜单表中添加以下菜单项：

### 1. 门禁告警菜单项
```sql
-- 假设门禁管理的父菜单ID为 [门禁管理父菜单ID]
INSERT INTO sys_menu (
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    is_frame,
    is_cache,
    menu_type,
    visible,
    status,
    perms,
    icon,
    create_by,
    create_time,
    remark
) VALUES (
    '门禁告警',                    -- 菜单名称
    [门禁管理父菜单ID],            -- 父菜单ID（需要替换为实际的门禁管理菜单ID）
    4,                            -- 排序（通行记录=1，门禁列表=2，访问记录=3，告警=4）
    'warning',                    -- 路由地址
    'guard/warning/index',        -- 组件路径
    1,                            -- 是否为外链（1是 0否）
    0,                            -- 是否缓存（0缓存 1不缓存）
    'C',                          -- 菜单类型（M目录 C菜单 F按钮）
    '0',                          -- 显示状态（0显示 1隐藏）
    '0',                          -- 菜单状态（0正常 1停用）
    'guard:warning:list',         -- 权限标识
    'warning',                    -- 菜单图标
    'admin',                      -- 创建者
    NOW(),                        -- 创建时间
    '门禁告警菜单'                 -- 备注
);
```

### 2. 权限按钮（可选）
如果需要添加操作按钮权限，可以添加以下按钮权限：

```sql
-- 查询按钮
INSERT INTO sys_menu (
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    is_frame,
    is_cache,
    menu_type,
    visible,
    status,
    perms,
    create_by,
    create_time
) VALUES (
    '告警查询',
    [门禁告警菜单ID],              -- 上面创建的门禁告警菜单ID
    1,
    '#',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'guard:warning:query',
    'admin',
    NOW()
);

-- 查看按钮
INSERT INTO sys_menu (
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    is_frame,
    is_cache,
    menu_type,
    visible,
    status,
    perms,
    create_by,
    create_time
) VALUES (
    '告警查看',
    [门禁告警菜单ID],              -- 上面创建的门禁告警菜单ID
    2,
    '#',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'guard:warning:view',
    'admin',
    NOW()
);
```

## 路由配置说明
由于项目使用动态路由，菜单配置完成后：

1. **路由路径**: `/guard/warning`
2. **组件路径**: `guard/warning/index`
3. **权限标识**: `guard:warning:list`

## API接口说明
- **接口地址**: `/hardware/pike/warning`
- **请求方法**: GET
- **分页参数**: `current` (当前页), `size` (每页大小)
- **查询参数**: 
  - `deviceId`: 设备ID
  - `warningType`: 告警类型
  - `reserved`: 报警类型 (0-4)
  - `startTime`: 开始时间
  - `endTime`: 结束时间

## 数据字段说明
根据提供的字段注释，主要字段包括：

### 核心字段
- `warningId`: 告警ID (主键，自增)
- `warningDateTime`: 报警日期及时间
- `deviceId`: 报警设备ID
- `warningType`: 告警类型
- `warningMemo`: 备注
- `reserved`: 报警类型分类
- `modifiedUser`: 修改人
- `modifiedDateTime`: 修改时间

### 报警类型分类 (reserved字段)
- `'0'`: 非法打开报警或未关闭
- `'1'`: 撤销报警
- `'2'`: 防撬报警
- `'3'`: 门未关闭
- `'4'`: 非法打开

## 功能特性

### 1. 多条件搜索
- 设备ID搜索
- 告警类型搜索
- 报警类型下拉选择
- 告警时间范围选择

### 2. 状态标签显示
不同报警类型使用不同颜色的标签：
- **红色 (danger)**: 非法打开报警或未关闭、非法打开
- **绿色 (success)**: 撤销报警
- **橙色 (warning)**: 防撬报警
- **蓝色 (info)**: 门未关闭

### 3. 告警详情查看
点击"查看"按钮可以弹出详情对话框，显示完整的告警信息。

### 4. 数据处理
- 时间字段统一格式化显示
- 空值字段显示为"--"或"无"
- 支持大数据量分页显示

## 页面布局
- **搜索区域**: 设备ID、告警类型、报警类型、时间范围搜索
- **数据表格**: 显示告警列表，包含所有关键字段
- **操作列**: 提供查看详情功能
- **分页组件**: 支持分页浏览
- **详情对话框**: 弹窗显示完整告警信息

## 配置完成后
1. 重启后端服务
2. 清除前端缓存
3. 重新登录系统
4. 在门禁管理菜单下应该能看到"门禁告警"菜单项

## 注意事项
1. 需要确保用户角色具有 `guard:warning:list` 权限
2. 菜单的 `parent_id` 需要设置为正确的门禁管理父菜单ID
3. 组件路径 `guard/warning/index` 对应前端文件 `src/views/guard/warning/index.vue`
4. 时间范围查询会转换为 `startTime` 和 `endTime` 参数传递给后端
5. 报警类型的颜色编码有助于快速识别告警严重程度
