import request from '@/utils/request'

// 查询门禁分页
export function listGuard(query) {
    return request({
        url: '/hardware/pkAccessControl',
        method: 'get',
        params: query
    })
}
//门禁通过主键查询单条数据
export function getGuard(id) {
    return request({
        url: '/hardware/pkAccessControl/' + id,
        method: 'get'
    })
}

// 查询门禁设备列表
export function listGuardDevice(query) {
    // 将前端分页参数转换为后端需要的格式
    const requestParams = {
        ...query,
        current: query.pageNum || 1,
        size: query.pageSize || 10
    };

    // 删除前端的分页参数，避免重复
    delete requestParams.pageNum;
    delete requestParams.pageSize;

    return request({
        url: '/hardware/pike/deviceList',
        method: 'get',
        params: requestParams
    })
}

// 查询门禁访问记录列表
export function listGuardAccessLog(query) {
    // 将前端分页参数转换为后端需要的格式
    const requestParams = {
        ...query,
        current: query.pageNum || 1,
        size: query.pageSize || 10
    };

    // 删除前端的分页参数，避免重复
    delete requestParams.pageNum;
    delete requestParams.pageSize;

    return request({
        url: '/hardware/pike/log',
        method: 'get',
        params: requestParams
    })
}